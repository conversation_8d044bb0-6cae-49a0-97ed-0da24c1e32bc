import { ethers } from 'ethers';
import { encrypt, decrypt, encodeForBlockchain, decodeFromBlockchain } from '../utils/crypto';
import deployments from '../contracts/deployments.json';

// Vault contract ABI (simplified for main functions)
const VAULT_ABI = [
  {
    "type": "function",
    "name": "store",
    "inputs": [{"name": "encryptedData", "type": "bytes", "internalType": "bytes"}],
    "outputs": [],
    "stateMutability": "nonpayable"
  },
  {
    "type": "function",
    "name": "retrieve",
    "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}],
    "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "getEntryMetadata",
    "inputs": [{"name": "id", "type": "uint256", "internalType": "uint256"}],
    "outputs": [
      {"name": "owner", "type": "address", "internalType": "address"},
      {"name": "timestamp", "type": "uint256", "internalType": "uint256"}
    ],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "getEntriesByOwner",
    "inputs": [{"name": "owner", "type": "address", "internalType": "address"}],
    "outputs": [{"name": "", "type": "uint256[]", "internalType": "uint256[]"}],
    "stateMutability": "view"
  },
  {
    "type": "function",
    "name": "hasAccess",
    "inputs": [{"name": "user", "type": "address", "internalType": "address"}],
    "outputs": [{"name": "", "type": "bool", "internalType": "bool"}],
    "stateMutability": "view"
  },
  {
    "type": "event",
    "name": "DataStored",
    "inputs": [
      {"name": "id", "type": "uint256", "indexed": true, "internalType": "uint256"},
      {"name": "owner", "type": "address", "indexed": true, "internalType": "address"},
      {"name": "timestamp", "type": "uint256", "indexed": false, "internalType": "uint256"}
    ]
  }
];

export interface VaultEntry {
  id: number;
  owner: string;
  timestamp: number;
  decryptedData?: string;
}

export class VaultService {
  private getContractAddress(): string {
    return deployments.monadTestnet.Vault.address;
  }

  private getProvider(): ethers.BrowserProvider | null {
    if (typeof window === 'undefined' || !window.ethereum) {
      return null;
    }
    return new ethers.BrowserProvider(window.ethereum);
  }

  private async getSigner(): Promise<ethers.JsonRpcSigner | null> {
    const provider = this.getProvider();
    if (!provider) return null;
    
    try {
      return await provider.getSigner();
    } catch (error) {
      console.error('Failed to get signer:', error);
      return null;
    }
  }

  async storeSecret(data: string, passphrase: string): Promise<{ success: boolean; id?: number; error?: string }> {
    try {
      const signer = await this.getSigner();
      if (!signer) {
        return { success: false, error: 'No wallet connection' };
      }

      // Encrypt the data
      const encryptedData = await encrypt(data, passphrase);
      const blockchainData = encodeForBlockchain(encryptedData);

      // Create contract instance
      const contract = new ethers.Contract(
        this.getContractAddress(),
        VAULT_ABI,
        signer
      );

      // Store on blockchain
      const tx = await contract.store(blockchainData);
      const receipt = await tx.wait();

      // Extract the ID from the DataStored event
      const event = receipt.logs.find((log: any) => {
        try {
          const parsedLog = contract.interface.parseLog(log);
          return parsedLog?.name === 'DataStored';
        } catch {
          return false;
        }
      });

      if (event) {
        const parsedLog = contract.interface.parseLog(event);
        const id = Number(parsedLog?.args.id);
        return { success: true, id };
      }

      return { success: false, error: 'Could not extract entry ID from transaction' };
    } catch (error: any) {
      console.error('Failed to store secret:', error);
      return { success: false, error: error.message };
    }
  }

  async retrieveSecret(id: number, passphrase: string): Promise<{ success: boolean; data?: string; error?: string }> {
    try {
      const provider = this.getProvider();
      if (!provider) {
        return { success: false, error: 'No provider available' };
      }

      // Create contract instance (read-only)
      const contract = new ethers.Contract(
        this.getContractAddress(),
        VAULT_ABI,
        provider
      );

      // Retrieve from blockchain
      const encryptedDataBytes = await contract.retrieve(id);
      
      if (!encryptedDataBytes || encryptedDataBytes === '0x') {
        return { success: false, error: 'No data found for this ID' };
      }

      // Decode and decrypt
      const encryptedData = decodeFromBlockchain(encryptedDataBytes);
      const decryptedData = await decrypt(encryptedData, passphrase);

      return { success: true, data: decryptedData };
    } catch (error: any) {
      console.error('Failed to retrieve secret:', error);
      return { success: false, error: error.message };
    }
  }

  async getUserEntries(userAddress: string): Promise<VaultEntry[]> {
    try {
      const provider = this.getProvider();
      if (!provider) {
        return [];
      }

      const contract = new ethers.Contract(
        this.getContractAddress(),
        VAULT_ABI,
        provider
      );

      const entryIds = await contract.getEntriesByOwner(userAddress);
      const entries: VaultEntry[] = [];

      for (const id of entryIds) {
        try {
          const [owner, timestamp] = await contract.getEntryMetadata(Number(id));
          entries.push({
            id: Number(id),
            owner,
            timestamp: Number(timestamp)
          });
        } catch (error) {
          console.error(`Failed to get metadata for entry ${id}:`, error);
        }
      }

      return entries.sort((a, b) => b.timestamp - a.timestamp);
    } catch (error: any) {
      console.error('Failed to get user entries:', error);
      return [];
    }
  }

  async hasAccess(userAddress: string): Promise<boolean> {
    try {
      const provider = this.getProvider();
      if (!provider) {
        return false;
      }

      const contract = new ethers.Contract(
        this.getContractAddress(),
        VAULT_ABI,
        provider
      );

      return await contract.hasAccess(userAddress);
    } catch (error: any) {
      console.error('Failed to check access:', error);
      return false;
    }
  }
}

// Export singleton instance
export const vaultService = new VaultService();
