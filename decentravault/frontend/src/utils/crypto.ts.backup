/**
 * Crypto utilities for client-side encryption/decryption using Web Crypto API
 */

export interface EncryptionResult {
  encryptedData: A<PERSON>yBuffer;
  iv: <PERSON><PERSON><PERSON><PERSON>uffer;
  salt: ArrayBuffer;
}

export interface DecryptionParams {
  encryptedData: <PERSON><PERSON><PERSON><PERSON>uffer;
  iv: <PERSON><PERSON><PERSON><PERSON>uffer;
  salt: <PERSON><PERSON><PERSON><PERSON>uffer;
}

/**
 * Generate a cryptographic key from a passphrase using PBKDF2
 */
export async function deriveKeyFromPassphrase(
  passphrase: string,
  salt: ArrayBuffer,
  iterations: number = 100000
): Promise<CryptoKey> {
  const encoder = new TextEncoder();
  const passphraseBuffer = encoder.encode(passphrase);
  
  // Import the passphrase as a key
  const baseKey = await crypto.subtle.importKey(
    'raw',
    passphraseBuffer,
    { name: 'PBKDF2' },
    false,
    ['deriveBits', 'deriveKey']
  );
  
  // Derive the AES key
  const key = await crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: salt,
      iterations: iterations,
      hash: 'SHA-256'
    },
    baseKey,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
  
  return key;
}

/**
 * Generate a random salt for key derivation
 */
export function generateSalt(): ArrayBuffer {
  return crypto.getRandomValues(new Uint8Array(16));
}

/**
 * Generate a random initialization vector
 */
export function generateIV(): ArrayBuffer {
  return crypto.getRandomValues(new Uint8Array(12));
}

/**
 * Encrypt data using AES-GCM
 */
export async function encrypt(
  data: string,
  passphrase: string
): Promise<EncryptionResult> {
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  
  const salt = generateSalt();
  const iv = generateIV();
  
  const key = await deriveKeyFromPassphrase(passphrase, salt);
  
  const encryptedData = await crypto.subtle.encrypt(
    {
      name: 'AES-GCM',
      iv: iv
    },
    key,
    dataBuffer
  );
  
  return {
    encryptedData,
    iv,
    salt
  };
}

/**
 * Decrypt data using AES-GCM
 */
export async function decrypt(
  params: DecryptionParams,
  passphrase: string
): Promise<string> {
  const key = await deriveKeyFromPassphrase(passphrase, params.salt);
  
  const decryptedBuffer = await crypto.subtle.decrypt(
    {
      name: 'AES-GCM',
      iv: params.iv
    },
    key,
    params.encryptedData
  );
  
  const decoder = new TextDecoder();
  return decoder.decode(decryptedBuffer);
}

/**
 * Convert ArrayBuffer to hex string
 */
export function arrayBufferToHex(buffer: ArrayBuffer): string {
  const byteArray = new Uint8Array(buffer);
  const hexCodes = [...byteArray].map(value => {
    const hexCode = value.toString(16);
    const paddedHexCode = hexCode.padStart(2, '0');
    return paddedHexCode;
  });
  return hexCodes.join('');
}

/**
 * Convert hex string to ArrayBuffer
 */
export function hexToArrayBuffer(hexString: string): ArrayBuffer {
  const bytes = new Uint8Array(hexString.length / 2);
  for (let i = 0; i < hexString.length; i += 2) {
    bytes[i / 2] = parseInt(hexString.substring(i, i + 2), 16);
  }
  return bytes.buffer;
}

/**
 * Encode encryption result to a format suitable for blockchain storage
 */
export function encodeForBlockchain(result: EncryptionResult): string {
  const combined = new Uint8Array(
    result.salt.byteLength + 
    result.iv.byteLength + 
    result.encryptedData.byteLength
  );
  
  let offset = 0;
  combined.set(new Uint8Array(result.salt), offset);
  offset += result.salt.byteLength;
  
  combined.set(new Uint8Array(result.iv), offset);
  offset += result.iv.byteLength;
  
  combined.set(new Uint8Array(result.encryptedData), offset);
  
  return arrayBufferToHex(combined.buffer);
}

/**
 * Decode encryption data from blockchain format
 */
export function decodeFromBlockchain(encodedData: string): DecryptionParams {
  const buffer = hexToArrayBuffer(encodedData);
  const data = new Uint8Array(buffer);
  
  // Salt is first 16 bytes
  const salt = data.slice(0, 16).buffer;
  
  // IV is next 12 bytes
  const iv = data.slice(16, 28).buffer;
  
  // Encrypted data is the rest
  const encryptedData = data.slice(28).buffer;
  
  return {
    salt,
    iv,
    encryptedData
  };
}

/**
 * Generate a secure random passphrase
 */
export function generateSecurePassphrase(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  
  return Array.from(array, byte => chars[byte % chars.length]).join('');
}
