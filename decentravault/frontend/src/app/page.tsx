'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { Shield, Github, Book } from 'lucide-react';

import { ConnectWallet } from '../components/ConnectWallet';
import { StoreSecret } from '../components/StoreSecret';
import { SecretList } from '../components/SecretList';
import { vaultService } from '../services/vault';

export default function Home() {
  const { address, isConnected } = useAccount();
  const [hasAccess, setHasAccess] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [error, setError] = useState('');
  const [checkingAccess, setCheckingAccess] = useState(false);

  // Check access when wallet is connected
  useEffect(() => {
    const checkAccess = async () => {
      if (!address) {
        setHasAccess(false);
        return;
      }

      setCheckingAccess(true);
      try {
        const access = await vaultService.hasAccess(address);
        setHasAccess(access);
        setError('');
      } catch (err: any) {
        console.error('Failed to check access:', err);
        setHasAccess(false);
        if (err.message.includes('not deployed')) {
          setError('Vault contract not deployed. Please deploy the contract first.');
        }
      } finally {
        setCheckingAccess(false);
      }
    };

    checkAccess();
  }, [address]);

  const handleSecretStored = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center gap-3">
              <Shield className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">DecentraVault</h1>
                <p className="text-sm text-gray-600">Privacy-Preserving Data Storage</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              <a
                href="https://github.com/your-username/decentravault"
                className="text-gray-600 hover:text-gray-900"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Github className="w-5 h-5" />
              </a>
              <a
                href="/docs"
                className="text-gray-600 hover:text-gray-900"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Book className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {!isConnected ? (
          <div className="bg-white rounded-lg shadow-lg p-8">
            <ConnectWallet />
          </div>
        ) : checkingAccess ? (
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4 animate-pulse" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Checking Access...</h2>
            <p className="text-gray-600">Verifying your permissions</p>
          </div>
        ) : !hasAccess ? (
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <Shield className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Required</h2>
            <p className="text-gray-600 mb-4">
              You need access permissions to use DecentraVault. Please contact the administrator.
            </p>
            <p className="text-sm text-gray-500 font-mono bg-gray-50 p-2 rounded">
              Your address: {address}
            </p>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Welcome Section */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center gap-4 mb-4">
                <Shield className="w-8 h-8 text-green-600" />
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Welcome to DecentraVault</h2>
                  <p className="text-gray-600">Securely store and retrieve encrypted data on the blockchain</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-blue-900 mb-2">🔐 Client-Side Encryption</h3>
                  <p className="text-sm text-blue-700">Your data is encrypted locally before being stored on-chain</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-green-900 mb-2">🔑 Your Keys, Your Data</h3>
                  <p className="text-sm text-green-700">Only you have access to your encryption keys</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-purple-900 mb-2">⚡ Blockchain Powered</h3>
                  <p className="text-sm text-purple-700">Immutable storage on the decentralized network</p>
                </div>
              </div>
            </div>

            {/* Store Secret Section */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <StoreSecret onSecretStored={handleSecretStored} />
            </div>

            {/* Secret List Section */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <SecretList refreshTrigger={refreshTrigger} />
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-600">
            <p>© 2025 DecentraVault. Built with privacy in mind.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
