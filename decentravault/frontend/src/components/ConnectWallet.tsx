'use client';

import { useAccount, useConnect, useDisconnect } from 'wagmi';
import { Wallet, LogOut, Shield } from 'lucide-react';

export function ConnectWallet() {
  const { address, isConnected } = useAccount();
  const { connectors, connect, isLoading } = useConnect();
  const { disconnect } = useDisconnect();

  if (isConnected && address) {
    return (
      <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg border">
        <Shield className="w-5 h-5 text-green-600" />
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-900">Connected</p>
          <p className="text-xs text-gray-600 font-mono">
            {address.slice(0, 6)}...{address.slice(-4)}
          </p>
        </div>
        <button
          onClick={() => disconnect()}
          className="flex items-center gap-2 px-3 py-2 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
        >
          <LogOut className="w-4 h-4" />
          Disconnect
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
        <Wallet className="w-5 h-5" />
        Connect Your Wallet
      </h3>
      <p className="text-sm text-gray-600">
        Connect your wallet to start using DecentraVault
      </p>
      <div className="grid gap-2">
        {connectors.map((connector) => (
          <button
            key={connector.id}
            onClick={() => connect({ connector })}
            disabled={isLoading}
            className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Wallet className="w-5 h-5 text-blue-600" />
            <span className="font-medium">{connector.name}</span>
            {isLoading && <span className="text-sm text-gray-500">Connecting...</span>}
          </button>
        ))}
      </div>
    </div>
  );
}
