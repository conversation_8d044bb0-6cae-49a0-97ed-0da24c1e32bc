'use client';

import { useState } from 'react';
import { Plus, Key, Eye, EyeOff } from 'lucide-react';
import { vaultService } from '../services/vault';
import { generateSecurePassphrase, storePassphrase } from '../utils/crypto';

interface StoreSecretProps {
  onSecretStored: () => void;
}

export function StoreSecret({ onSecretStored }: StoreSecretProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [secretData, setSecretData] = useState('');
  const [passphrase, setPassphrase] = useState('');
  const [showPassphrase, setShowPassphrase] = useState(false);
  const [isStoring, setIsStoring] = useState(false);
  const [error, setError] = useState('');

  const handleGeneratePassphrase = () => {
    const newPassphrase = generateSecurePassphrase(24);
    setPassphrase(newPassphrase);
  };

  const handleStore = async () => {
    if (!secretData.trim() || !passphrase.trim()) {
      setError('Please provide both secret data and passphrase');
      return;
    }

    setIsStoring(true);
    setError('');

    try {
      const id = await vaultService.storeSecret(secretData, passphrase);
      
      // Optionally store passphrase for this session
      storePassphrase(passphrase);
      
      // Reset form
      setSecretData('');
      setPassphrase('');
      setIsOpen(false);
      
      onSecretStored();
      
      alert(`Secret stored successfully with ID: ${id}`);
    } catch (err: any) {
      setError(err.message || 'Failed to store secret');
    } finally {
      setIsStoring(false);
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        <Plus className="w-4 h-4" />
        Store New Secret
      </button>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Store New Secret</h3>
        <button
          onClick={() => setIsOpen(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          ✕
        </button>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Secret Data
          </label>
          <textarea
            value={secretData}
            onChange={(e) => setSecretData(e.target.value)}
            placeholder="Enter your secret data (API keys, passwords, notes, etc.)"
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={4}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Encryption Passphrase
          </label>
          <div className="relative">
            <input
              type={showPassphrase ? 'text' : 'password'}
              value={passphrase}
              onChange={(e) => setPassphrase(e.target.value)}
              placeholder="Enter a strong passphrase"
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-20"
            />
            <button
              type="button"
              onClick={() => setShowPassphrase(!showPassphrase)}
              className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPassphrase ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
            <button
              type="button"
              onClick={handleGeneratePassphrase}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-800"
              title="Generate secure passphrase"
            >
              <Key className="w-4 h-4" />
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            This passphrase will be used to encrypt your data. Store it safely!
          </p>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <div className="flex gap-3">
          <button
            onClick={handleStore}
            disabled={isStoring || !secretData.trim() || !passphrase.trim()}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isStoring ? 'Storing...' : 'Store Secret'}
          </button>
          <button
            onClick={() => setIsOpen(false)}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}
