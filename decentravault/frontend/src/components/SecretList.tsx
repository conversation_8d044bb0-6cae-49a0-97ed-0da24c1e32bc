'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { Lock, Unlock, Calendar, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { vaultService } from '../services/vault';
import { getStoredPassphrase, clearStoredPassphrase } from '../utils/crypto';

interface VaultEntry {
  id: number;
  owner: string;
  timestamp: number;
}

interface SecretListProps {
  refreshTrigger: number;
}

export function SecretList({ refreshTrigger }: SecretListProps) {
  const { address } = useAccount();
  const [entries, setEntries] = useState<VaultEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [decryptedSecrets, setDecryptedSecrets] = useState<Record<number, string>>({});
  const [decryptingIds, setDecryptingIds] = useState<Set<number>>(new Set());
  const [error, setError] = useState('');

  const loadEntries = async () => {
    if (!address) return;

    setLoading(true);
    setError('');

    try {
      const userEntries = await vaultService.getUserEntries(address);
      setEntries(userEntries);
    } catch (err: any) {
      setError(err.message || 'Failed to load entries');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadEntries();
  }, [address, refreshTrigger]);

  const handleDecrypt = async (entryId: number) => {
    // First try to use stored passphrase
    let passphrase = getStoredPassphrase();
    
    if (!passphrase) {
      passphrase = prompt('Enter the passphrase for this secret:');
      if (!passphrase) return;
    }

    setDecryptingIds(prev => new Set(prev).add(entryId));

    try {
      const result = await vaultService.retrieveSecret(entryId, passphrase);
      if (result.success && result.data) {
        setDecryptedSecrets(prev => ({
          ...prev,
          [entryId]: result.data!
        }));
      } else {
        alert('Failed to decrypt: ' + (result.error || 'Invalid passphrase or corrupted data'));
        // Clear potentially wrong stored passphrase
        clearStoredPassphrase();
      }
    } catch (err: any) {
      alert('Failed to decrypt: ' + (err.message || 'Invalid passphrase or corrupted data'));
      // Clear potentially wrong stored passphrase
      clearStoredPassphrase();
    } finally {
      setDecryptingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(entryId);
        return newSet;
      });
    }
  };

  const hideSecret = (entryId: number) => {
    setDecryptedSecrets(prev => {
      const newSecrets = { ...prev };
      delete newSecrets[entryId];
      return newSecrets;
    });
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  if (loading && entries.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading your secrets...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-600">{error}</p>
        <button
          onClick={loadEntries}
          className="mt-2 text-sm text-red-700 underline hover:text-red-800"
        >
          Try again
        </button>
      </div>
    );
  }

  if (entries.length === 0) {
    return (
      <div className="text-center p-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
        <Lock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No secrets stored yet</h3>
        <p className="text-gray-600">Store your first secret to get started</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Your Secrets</h3>
        <button
          onClick={loadEntries}
          disabled={loading}
          className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      <div className="grid gap-4">
        {entries.map((entry) => {
          const isDecrypted = decryptedSecrets[entry.id];
          const isDecrypting = decryptingIds.has(entry.id);

          return (
            <div key={entry.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-medium text-gray-900">Secret #{entry.id}</h4>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {formatTimestamp(entry.timestamp)}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {isDecrypted ? (
                    <button
                      onClick={() => hideSecret(entry.id)}
                      className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                    >
                      <EyeOff className="w-3 h-3" />
                      Hide
                    </button>
                  ) : (
                    <button
                      onClick={() => handleDecrypt(entry.id)}
                      disabled={isDecrypting}
                      className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
                    >
                      {isDecrypting ? (
                        <RefreshCw className="w-3 h-3 animate-spin" />
                      ) : (
                        <Eye className="w-3 h-3" />
                      )}
                      {isDecrypting ? 'Decrypting...' : 'Decrypt'}
                    </button>
                  )}
                </div>
              </div>

              {isDecrypted && (
                <div className="bg-gray-50 border border-gray-200 rounded p-3">
                  <div className="flex items-center gap-2 mb-2">
                    <Unlock className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-green-700">Decrypted Content</span>
                  </div>
                  <pre className="text-sm text-gray-800 whitespace-pre-wrap break-words">
                    {isDecrypted}
                  </pre>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
