{"name": "monad-faas", "version": "1.0.0", "description": "Monad FaaS CLI - Deploy serverless functions on-chain", "main": "dist/index.js", "bin": {"monad-faas": "./dist/index.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "test": "echo \"No tests yet\" && exit 0"}, "keywords": ["blockchain", "serverless", "faas", "monad", "web3"], "author": "MonadBot", "license": "MIT", "dependencies": {"@types/node": "^22.15.27", "axios": "^1.9.0", "chalk": "^5.4.1", "commander": "^14.0.0", "ethers": "^6.14.3", "inquirer": "^12.6.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}