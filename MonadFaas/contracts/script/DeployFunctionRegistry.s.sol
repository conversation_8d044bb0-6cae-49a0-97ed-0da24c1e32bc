// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Script.sol";
import "../src/FunctionRegistry.sol";

contract DeployFunctionRegistry is Script {
    function run() external {
        // Use default Anvil private key for local testing
        uint256 deployerPrivateKey = 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80;
        
        vm.startBroadcast(deployerPrivateKey);
        
        FunctionRegistry registry = new FunctionRegistry();
        
        console.log("FunctionRegistry deployed at:", address(registry));
        console.log("Deployer:", vm.addr(deployerPrivateKey));
        console.log("Next Function ID:", registry.nextFunctionId());
        console.log("Max Gas Limit:", registry.maxGasLimit());
        
        vm.stopBroadcast();
    }
}
